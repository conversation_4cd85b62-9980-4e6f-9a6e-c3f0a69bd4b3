"""
Simple Firecrawl-based job scraper for JoMaDe application.
This module handles scraping job URLs using Firecrawl API and extracting job information using OpenAI.
Includes intelligent caching using Firecrawl's session management as single source of truth.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import re
import asyncio
import signal
import json
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FuturesTimeoutError

from firecrawl import FirecrawlApp
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JobScraper:
    """Simple job scraper using Firecrawl and OpenAI with intelligent caching."""

    def __init__(self, timeout_seconds: int = 300, cache_hours: int = 24):
        """Initialize the scraper with API keys from environment.

        Args:
            timeout_seconds: Maximum time to wait for each URL scraping operation (default: 5 minutes)
            cache_hours: Hours to consider crawl results as fresh (default: 24 hours)
        """
        self.firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.timeout_seconds = timeout_seconds
        self.cache_hours = cache_hours

        if not self.firecrawl_api_key:
            raise ValueError("FIRECRAWL_API_KEY not found in environment variables")
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")

        # Initialize Firecrawl
        self.firecrawl = FirecrawlApp(api_key=self.firecrawl_api_key)

        # Initialize OpenAI
        openai.api_key = self.openai_api_key
        self.openai_client = openai.OpenAI(api_key=self.openai_api_key)

        # Initialize crawl session cache
        self.crawl_cache_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'firecrawl_cache.json')
        self.crawl_cache = self._load_crawl_cache()

        logger.info(f"JobScraper initialized successfully with {timeout_seconds}s timeout and {cache_hours}h cache")

    def _load_crawl_cache(self) -> Dict[str, Any]:
        """Load crawl session cache from file."""
        try:
            if os.path.exists(self.crawl_cache_file):
                with open(self.crawl_cache_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load crawl cache: {e}")
        return {}

    def _save_crawl_cache(self):
        """Save crawl session cache to file."""
        try:
            os.makedirs(os.path.dirname(self.crawl_cache_file), exist_ok=True)
            with open(self.crawl_cache_file, 'w') as f:
                json.dump(self.crawl_cache, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save crawl cache: {e}")

    def _get_cached_crawl_id(self, url: str) -> Optional[str]:
        """Get cached crawl ID for URL if it's still fresh."""
        url_cache = self.crawl_cache.get(url)
        if not url_cache:
            return None

        # Check if cache is still fresh
        cached_time = datetime.fromisoformat(url_cache['timestamp'])
        if datetime.now() - cached_time < timedelta(hours=self.cache_hours):
            return url_cache['crawl_id']

        return None

    def _cache_crawl_id(self, url: str, crawl_id: str):
        """Cache crawl ID for URL with current timestamp."""
        self.crawl_cache[url] = {
            'crawl_id': crawl_id,
            'timestamp': datetime.now().isoformat()
        }
        self._save_crawl_cache()

    def _get_active_crawls(self) -> List[Dict[str, Any]]:
        """Get list of active crawls from Firecrawl."""
        try:
            import requests
            headers = {"Authorization": f"Bearer {self.firecrawl_api_key}"}
            response = requests.get(f"https://api.firecrawl.dev/v1/crawl/active", headers=headers)

            if response.status_code == 200:
                data = response.json()
                return data.get('crawls', [])
            else:
                logger.warning(f"Failed to get active crawls: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"Error getting active crawls: {e}")
            return []

    def _crawl_url_with_timeout(self, url: str, source_prefix: str, log_callback=None) -> List[Dict[str, Any]]:
        """
        Internal method to crawl URL with timeout handling.
        This runs the actual crawling logic that can be interrupted.
        """
        return self._crawl_url_internal(url, source_prefix, log_callback)

    def crawl_url(self, url: str, source_prefix: str, log_callback=None, force_scrape: bool = False) -> List[Dict[str, Any]]:
        """
        Crawl a website for job listings using Firecrawl with intelligent caching.

        Args:
            url: The URL to crawl
            source_prefix: The three-letter prefix for this source (e.g., AAA)
            log_callback: Optional callback function for real-time logging
            force_scrape: If True, bypass cache and force new crawl

        Returns:
            List of job dictionaries
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        try:
            # Check for cached crawl results first (unless force_scrape is True)
            if not force_scrape:
                cached_crawl_id = self._get_cached_crawl_id(url)
                if cached_crawl_id:
                    log("info", f"🔄 CACHE HIT: Found recent crawl for {url} (ID: {cached_crawl_id[:8]}...)")
                    logger.info(f"Using cached crawl {cached_crawl_id} for URL: {url}")

                    try:
                        # Try to get the cached crawl results
                        cached_results = self._get_crawl_results(cached_crawl_id, url, source_prefix, log_callback)
                        if cached_results:
                            log("success", f"✅ CACHE SUCCESS: Retrieved {len(cached_results)} jobs from cached crawl")
                            return cached_results
                        else:
                            log("warning", f"⚠️ CACHE MISS: Cached crawl {cached_crawl_id[:8]}... no longer available")
                    except Exception as e:
                        log("warning", f"⚠️ CACHE ERROR: Failed to retrieve cached results: {str(e)}")
                        logger.warning(f"Failed to retrieve cached crawl {cached_crawl_id}: {e}")

            log("info", f"🕷️ NEW CRAWL: {url} (prefix: {source_prefix}) - Timeout: {self.timeout_seconds}s")
            logger.info(f"Starting new crawl for URL: {url} with prefix: {source_prefix}, timeout: {self.timeout_seconds}s")

            # Use ThreadPoolExecutor with timeout to prevent hanging
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(self._crawl_url_with_timeout, url, source_prefix, log_callback)
                try:
                    result = future.result(timeout=self.timeout_seconds)
                    return result
                except FuturesTimeoutError:
                    log("error", f"⏰ TIMEOUT: Crawling {url} exceeded {self.timeout_seconds} seconds")
                    logger.warning(f"Crawling {url} timed out after {self.timeout_seconds} seconds")
                    return []
                except Exception as e:
                    log("error", f"❌ ERROR in timeout wrapper for {url}: {str(e)}")
                    logger.error(f"Error in timeout wrapper for {url}: {str(e)}")
                    return []

        except Exception as e:
            log("error", f"❌ ERROR setting up crawl for {url}: {str(e)}")
            logger.error(f"Error setting up crawl for {url}: {str(e)}")
            return []

    def _get_crawl_results(self, crawl_id: str, url: str, source_prefix: str, log_callback=None) -> List[Dict[str, Any]]:
        """Retrieve results from a cached Firecrawl crawl session."""
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        try:
            log("info", f"📥 RETRIEVING: Cached crawl results for {url}")

            # Use Firecrawl's check_crawl_status method to get results
            crawl_status = self.firecrawl.check_crawl_status(crawl_id)

            if not crawl_status or crawl_status.status != 'completed':
                log("warning", f"⚠️ Cached crawl {crawl_id[:8]}... is not completed (status: {crawl_status.status if crawl_status else 'None'})")
                return []

            # Process the cached crawl data
            pages_data = crawl_status.data if crawl_status.data else []

            if not pages_data:
                log("warning", f"⚠️ No data found in cached crawl {crawl_id[:8]}...")
                return []

            log("info", f"📄 Processing {len(pages_data)} cached pages from crawl {crawl_id[:8]}...")

            # Process all cached pages to extract jobs (reuse existing logic)
            all_jobs = []
            for i, page in enumerate(pages_data):
                # Handle different page data structures from Firecrawl SDK
                page_markdown = None
                page_url = url

                # Current Firecrawl SDK returns dict with 'markdown' and 'metadata' keys
                if isinstance(page, dict):
                    page_markdown = page.get('markdown')
                    if page.get('metadata') and isinstance(page['metadata'], dict):
                        page_url = page['metadata'].get('sourceURL', url)
                # Fallback for object-based responses
                elif hasattr(page, 'markdown'):
                    page_markdown = page.markdown
                    if hasattr(page, 'metadata'):
                        if hasattr(page.metadata, 'sourceURL'):
                            page_url = page.metadata.sourceURL
                        elif isinstance(page.metadata, dict):
                            page_url = page.metadata.get('sourceURL', url)

                if not page_markdown:
                    log("warning", f"⚠️ No markdown content found for cached page {i+1}")
                    continue

                log("info", f"🔍 Extracting jobs from cached page {i+1}/{len(pages_data)}: {page_url}")

                # Extract jobs from this page using existing logic
                page_jobs = self._extract_jobs_with_llm(page_markdown, page_url, source_prefix, i+1, log_callback)
                all_jobs.extend(page_jobs)

            log("success", f"✅ CACHED EXTRACTION: Found {len(all_jobs)} jobs from cached crawl")
            return all_jobs

        except Exception as e:
            log("error", f"❌ ERROR retrieving cached crawl {crawl_id[:8]}...: {str(e)}")
            logger.error(f"Error retrieving cached crawl {crawl_id}: {str(e)}")
            return []

    def _crawl_url_internal(self, url: str, source_prefix: str, log_callback=None) -> List[Dict[str, Any]]:
        """
        Internal crawling method that performs the actual Firecrawl API calls.

        Args:
            url: The URL to crawl
            source_prefix: The three-letter prefix for this source (e.g., AAA)
            log_callback: Optional callback function for real-time logging

        Returns:
            List of job dictionaries
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        try:
            log("info", f"📡 Calling Firecrawl CRAWL API for {url}...")
            from firecrawl import ScrapeOptions

            # Use async crawl to get crawl ID for caching, then wait for completion
            log("info", f"🚀 Starting async crawl for {url}...")
            async_crawl_result = self.firecrawl.async_crawl_url(
                url,
                limit=10,  # Reduced from 50 to 10 pages to prevent timeouts
                scrape_options=ScrapeOptions(
                    formats=['markdown'],
                    only_main_content=True
                )
            )

            if not async_crawl_result or not async_crawl_result.success:
                log("error", f"❌ Failed to start crawl for {url}")
                logger.warning(f"Failed to start crawl for {url}")
                return []

            crawl_id = async_crawl_result.id
            if not crawl_id:
                log("error", f"❌ No crawl ID returned for {url}")
                logger.warning(f"No crawl ID returned for {url}")
                return []

            # Cache the crawl ID for future use
            self._cache_crawl_id(url, crawl_id)
            log("info", f"💾 Cached crawl ID {crawl_id[:8]}... for {url}")

            # Wait for crawl completion and get results with polling
            log("info", f"⏳ Waiting for crawl {crawl_id[:8]}... to complete...")

            import time
            max_wait_time = self.timeout_seconds
            poll_interval = 5  # Check every 5 seconds
            elapsed_time = 0

            while elapsed_time < max_wait_time:
                crawl_result = self.firecrawl.check_crawl_status(crawl_id)

                if not crawl_result:
                    log("error", f"❌ Failed to get crawl status for {crawl_id[:8]}...")
                    logger.warning(f"Failed to get crawl status for {crawl_id}")
                    return []

                if crawl_result.status == 'completed':
                    log("success", f"✅ Crawl {crawl_id[:8]}... completed successfully!")
                    break
                elif crawl_result.status == 'failed':
                    log("error", f"❌ Crawl {crawl_id[:8]}... failed")
                    logger.warning(f"Crawl {crawl_id} failed for {url}")
                    return []
                elif crawl_result.status == 'scraping':
                    log("info", f"🔄 Crawl {crawl_id[:8]}... still in progress... ({elapsed_time}s elapsed)")
                    time.sleep(poll_interval)
                    elapsed_time += poll_interval
                else:
                    log("warning", f"⚠️ Crawl {crawl_id[:8]}... unknown status: {crawl_result.status}")
                    time.sleep(poll_interval)
                    elapsed_time += poll_interval

            # Check if we timed out
            if elapsed_time >= max_wait_time:
                log("error", f"❌ Crawl {crawl_id[:8]}... timed out after {max_wait_time}s")
                logger.warning(f"Crawl {crawl_id} timed out for {url}")
                return []

            # Final check to ensure we have a completed crawl
            if not crawl_result or crawl_result.status != 'completed':
                log("error", f"❌ Crawl {crawl_id[:8]}... not completed (final status: {crawl_result.status if crawl_result else 'None'})")
                logger.warning(f"Crawl {crawl_id} not completed for {url}")
                return []

            # Get the crawled pages data
            pages_data = crawl_result.data if crawl_result.data else []

            if not pages_data:
                log("error", f"❌ No content crawled from {url}")
                logger.warning(f"No content crawled from {url}")
                return []
            log("success", f"✅ Crawled {len(pages_data)} pages from {url}")
            logger.info(f"Crawled {len(pages_data)} pages from {url}")

            # Process all crawled pages to extract jobs
            all_jobs = []
            for i, page in enumerate(pages_data):
                # Handle different page data structures from Firecrawl SDK
                page_markdown = None
                page_url = url

                # Current Firecrawl SDK returns dict with 'markdown' and 'metadata' keys
                if isinstance(page, dict):
                    page_markdown = page.get('markdown')
                    if page.get('metadata') and isinstance(page['metadata'], dict):
                        page_url = page['metadata'].get('sourceURL', url)
                # Fallback for object-based responses
                elif hasattr(page, 'markdown'):
                    page_markdown = page.markdown
                    if hasattr(page, 'metadata'):
                        if hasattr(page.metadata, 'sourceURL'):
                            page_url = page.metadata.sourceURL
                        elif isinstance(page.metadata, dict):
                            page_url = page.metadata.get('sourceURL', url)

                if page_markdown:
                    log("info", f"🔍 Processing page {i+1}/{len(pages_data)}: {page_url}")

                    # Extract job information using OpenAI
                    jobs = self._extract_jobs_with_llm(
                        page_markdown,
                        page_url,
                        source_prefix,
                        page_number=i+1,
                        log_callback=log_callback
                    )

                    if jobs:
                        all_jobs.extend(jobs)
                        log("success", f"  ✅ Found {len(jobs)} jobs on this page")
                else:
                    log("warning", f"⚠️  Page {i+1} has no markdown content, skipping")
                    # Debug: show what we actually got
                    if isinstance(page, dict):
                        log("warning", f"     Available keys: {list(page.keys())}")
                    else:
                        log("warning", f"     Page type: {type(page)}, attributes: {[attr for attr in dir(page) if not attr.startswith('_')]}")

            log("success", f"🎯 TOTAL: Extracted {len(all_jobs)} jobs from {url}")
            logger.info(f"Successfully extracted {len(all_jobs)} jobs from {url}")
            return all_jobs

        except Exception as e:
            log("error", f"❌ ERROR crawling {url}: {str(e)}")
            logger.error(f"Error crawling {url}: {str(e)}")
            return []

    def _extract_jobs_with_llm(self, content: str, source_url: str, source_prefix: str, page_number: int = 1, log_callback=None) -> List[Dict[str, Any]]:
        """
        Extract job information from scraped content using OpenAI.

        Args:
            content: The scraped markdown content
            source_url: The original URL
            source_prefix: The three-letter prefix for this source
            page_number: The page number being processed
            log_callback: Optional callback function for real-time logging

        Returns:
            List of job dictionaries
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)
        try:
            # Improved job extraction prompt - focuses on actual job postings, not navigation/metadata
            prompt = f"""
            Extract ONLY actual job postings from this content. Focus on complete job descriptions with detailed information.

            IMPORTANT RULES:
            1. Only extract jobs that have DETAILED descriptions, requirements, or responsibilities
            2. Ignore navigation menus, breadcrumbs, image alt text, and lists of job titles without details
            3. Ignore content that appears to be just job title lists or category navigation
            4. Each job must have substantial content beyond just a title and location
            5. If you find multiple jobs on the same page, each must have its own unique detail URL
            6. If jobs share the same URL, only extract the MAIN job posting from that page

            Look for these indicators of a REAL job posting:
            - Detailed job description or responsibilities section
            - Requirements or qualifications section
            - Salary information or benefits
            - Application instructions or contact information
            - Company description related to the specific role

            For each ACTUAL job posting found, return:
            {{
                "title": "exact job title from the main job posting",
                "company": "company name if found, otherwise 'Not specified'",
                "location": "location if found, otherwise 'Not specified'",
                "summary": "brief summary from actual job description (max 150 chars)",
                "detail_url": "direct job URL if found, otherwise '{source_url}'"
            }}

            Return ONLY a valid JSON array. If you only find one main job posting, return an array with one job.
            Do NOT extract jobs from navigation elements, image alt text, or title lists.

            Content:
            {content[:8000]}
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",  # Better model for extraction
                messages=[
                    {"role": "system", "content": "You are a job listing extraction expert. You MUST return valid JSON array format. Extract ALL job postings from the content, even if information is incomplete."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,  # More tokens for better extraction
                temperature=0.0   # Deterministic output
            )

            # Parse the response
            response_text = response.choices[0].message.content.strip()
            log("info", f"🤖 LLM Response length: {len(response_text)} characters")

            # Try to extract JSON from the response
            import json
            import re

            jobs_data = []
            try:
                # First try: direct JSON parsing
                jobs_data = json.loads(response_text)
                log("success", f"✅ Direct JSON parsing successful: {len(jobs_data)} jobs")
            except json.JSONDecodeError:
                try:
                    # Second try: extract JSON array from response
                    json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                    if json_match:
                        jobs_data = json.loads(json_match.group())
                        log("success", f"✅ Regex JSON extraction successful: {len(jobs_data)} jobs")
                    else:
                        log("error", "❌ No JSON array found in LLM response")
                        log("error", f"Response preview: {response_text[:500]}...")
                        return []
                except json.JSONDecodeError as e:
                    log("error", f"❌ JSON parsing failed: {str(e)}")
                    log("error", f"Response preview: {response_text[:500]}...")
                    return []

            # Process and format the jobs with URL validation and full description fetching
            jobs = []
            seen_urls = set()  # Track URLs to prevent duplicates within the same extraction

            for i, job_data in enumerate(jobs_data[:20]):  # Limit to 20 jobs per page
                job_id = f"{source_prefix}{page_number:02d}{i+1:02d}"  # e.g., AAA0101, AAA0102

                # Get the job URL
                job_url = job_data.get("detail_url", job_data.get("link", source_url))

                # Skip jobs with duplicate URLs within the same extraction
                if job_url in seen_urls and job_url != source_url:
                    log("warning", f"⚠️ Skipping duplicate URL in extraction: {job_url}")
                    continue

                seen_urls.add(job_url)

                # Get full job description by fetching the individual job URL
                full_description = self._fetch_full_job_description(job_url, job_data, log)

                job = {
                    "id": job_id,
                    "title": job_data.get("title", "Unknown Position"),
                    "company": job_data.get("company", "Not specified"),
                    "location": job_data.get("location", "Not specified"),
                    "description": full_description,
                    "source": source_prefix,
                    "link": job_url,
                    "isShortlisted": False,
                    "scraped_at": datetime.now().isoformat()
                }
                jobs.append(job)

            return jobs

        except Exception as e:
            logger.error(f"Error extracting jobs with LLM: {str(e)}")
            return []

    def _fetch_full_job_description(self, job_url: str, job_data: Dict[str, Any], log) -> str:
        """
        Fetch full job description from individual job URL.

        Args:
            job_url: URL to fetch full description from
            job_data: Basic job data already extracted
            log: Logging callback function

        Returns:
            Full job description or fallback to basic description
        """
        # If URL is the same as source URL, we already have the best description available
        basic_description = job_data.get("summary", job_data.get("description", "No description available"))

        # Don't fetch if we already have a substantial description (more than just salary)
        if len(basic_description) > 100 and not self._is_salary_only_description(basic_description):
            return basic_description[:500]  # Return first 500 chars of good description

        # Don't fetch if URL looks like a listing page rather than individual job page
        if any(indicator in job_url.lower() for indicator in ['stellenangebote', 'jobs', 'karriere', 'list']):
            if job_url == job_data.get("detail_url"):
                # This is supposed to be a detail URL, try to fetch it
                pass
            else:
                # This is clearly a listing URL, don't fetch
                return basic_description[:200]

        try:
            log("info", f"🔍 Fetching full description for: {job_data.get('title', 'Unknown')}")

            # Use Firecrawl to get the individual job page content
            crawl_result = self.firecrawl_app.scrape_url(
                job_url,
                params={
                    'formats': ['markdown'],
                    'timeout': 30000,
                    'waitFor': 2000
                }
            )

            if not crawl_result.get('success', False):
                log("warning", f"⚠️ Failed to fetch job details from {job_url}")
                return basic_description[:200]

            content = crawl_result.get('data', {}).get('markdown', '')
            if not content:
                log("warning", f"⚠️ No content found at {job_url}")
                return basic_description[:200]

            # Extract job description using LLM
            full_description = self._extract_job_description_with_llm(content, job_data, log)

            if full_description and len(full_description) > len(basic_description):
                log("success", f"✅ Enhanced description for: {job_data.get('title', 'Unknown')}")
                return full_description[:800]  # Limit to 800 chars for storage
            else:
                return basic_description[:200]

        except Exception as e:
            log("warning", f"⚠️ Error fetching full description: {str(e)}")
            return basic_description[:200]

    def _is_salary_only_description(self, description: str) -> bool:
        """Check if description contains only salary information."""
        description_lower = description.lower()
        salary_indicators = ['eur', '€', 'gehalt', 'salary', '000', 'k€', 'tsd']
        word_count = len(description.split())

        # If description is very short and contains salary indicators, it's likely salary-only
        return word_count < 10 and any(indicator in description_lower for indicator in salary_indicators)

    def _extract_job_description_with_llm(self, content: str, job_data: Dict[str, Any], log) -> str:
        """
        Extract comprehensive job description from job page content using LLM.

        Args:
            content: Full page content from job URL
            job_data: Basic job information for context
            log: Logging callback function

        Returns:
            Comprehensive job description
        """
        try:
            job_title = job_data.get('title', 'Unknown Position')

            prompt = f"""
            Extract a comprehensive job description from this job posting page.

            Job Title: {job_title}

            Focus on extracting:
            1. Key responsibilities and duties
            2. Required qualifications and skills
            3. Experience requirements
            4. Company information relevant to the role
            5. Benefits or compensation details
            6. Location and work arrangement details

            IMPORTANT:
            - Extract ONLY information relevant to this specific job posting
            - Ignore navigation menus, other job listings, or website boilerplate
            - Focus on the main job description content
            - Keep the description concise but comprehensive (max 400 words)
            - If multiple jobs are mentioned, focus only on the one matching the title: "{job_title}"

            Return only the job description text, no JSON or formatting.

            Content:
            {content[:6000]}
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a job description extraction specialist. Extract comprehensive, relevant job descriptions from web content."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=500
            )

            description = response.choices[0].message.content.strip()

            # Validate that we got a meaningful description
            if len(description) > 50 and not self._is_salary_only_description(description):
                return description
            else:
                log("warning", f"⚠️ LLM returned insufficient description for {job_title}")
                return job_data.get("summary", "No detailed description available")

        except Exception as e:
            log("warning", f"⚠️ Error extracting description with LLM: {str(e)}")
            return job_data.get("summary", "No detailed description available")

    def scrape_multiple_urls(self, urls_with_prefixes: List[Dict[str, str]], log_callback=None, force_scrape: bool = False) -> Dict[str, Any]:
        """
        Scrape multiple URLs for job listings with intelligent caching.

        Args:
            urls_with_prefixes: List of dicts with 'url' and 'prefix' keys
            log_callback: Optional callback function for real-time logging
            force_scrape: If True, bypass cache and force new crawls

        Returns:
            Dictionary with scraping results
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        log("info", f"🚀 STARTING BATCH SCRAPING: {len(urls_with_prefixes)} URLs")
        logger.info(f"Starting batch scraping of {len(urls_with_prefixes)} URLs")

        all_jobs = []
        successful_urls = 0
        failed_urls = []
        successful_prefixes = []

        for i, url_data in enumerate(urls_with_prefixes, 1):
            url = url_data.get('url', '')
            prefix = url_data.get('prefix', 'AAA')

            if not url:
                log("warning", f"⚠️  Skipping empty URL at position {i}")
                continue

            log("info", f"📋 Processing {i}/{len(urls_with_prefixes)}: {prefix}")
            log("info", f"🔗 URL: {url}")

            # Add progress information to the log callback
            if log_callback:
                log_callback("progress", f"Processing {i}/{len(urls_with_prefixes)}: {prefix}", {
                    "current_index": i,
                    "total_urls": len(urls_with_prefixes),
                    "current_prefix": prefix,
                    "current_url": url,
                    "completed_urls": i - 1,
                    "successful_urls": successful_urls,
                    "failed_urls": len(failed_urls)
                })

            jobs = self.crawl_url(url, prefix, log_callback, force_scrape)
            if jobs:
                all_jobs.extend(jobs)
                successful_urls += 1
                successful_prefixes.append(prefix)
                log("success", f"✅ Success: {len(jobs)} jobs added (Total: {len(all_jobs)})")
                if log_callback:
                    log_callback("success", f"✅ Success: {len(jobs)} jobs added (Total: {len(all_jobs)})", {
                        "jobs_found": len(jobs),
                        "total_jobs": len(all_jobs),
                        "successful_urls": successful_urls,
                        "current_prefix": prefix
                    })
            else:
                failed_urls.append(url)
                log("error", f"❌ Failed: No jobs found")
                if log_callback:
                    log_callback("error", f"❌ Failed: No jobs found", {
                        "failed_urls": len(failed_urls) + 1,
                        "current_prefix": prefix
                    })

        log("info", f"🎯 SCRAPING COMPLETE:")
        log("info", f"   📊 Total Jobs: {len(all_jobs)}")
        log("info", f"   ✅ Successful URLs: {successful_urls}/{len(urls_with_prefixes)}")
        if failed_urls:
            log("warning", f"   ❌ Failed URLs: {len(failed_urls)}")

        result = {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "url_count": len(urls_with_prefixes),
            "successful_urls": successful_urls,
            "successful_prefixes": successful_prefixes,
            "failed_urls": failed_urls,
            "job_count": len(all_jobs),
            "jobs": all_jobs,
            "message": f"Scraped {len(all_jobs)} jobs from {successful_urls}/{len(urls_with_prefixes)} URLs"
        }

        logger.info(f"Batch scraping completed: {len(all_jobs)} jobs from {successful_urls}/{len(urls_with_prefixes)} URLs")
        return result


def create_scraper(timeout_seconds: int = 180, cache_hours: int = 24) -> Optional[JobScraper]:
    """
    Factory function to create a JobScraper instance with intelligent caching.
    Returns None if API keys are not configured.

    Args:
        timeout_seconds: Maximum time to wait for each URL scraping operation (default: 3 minutes)
        cache_hours: Hours to consider crawl results as fresh (default: 24 hours)
    """
    try:
        print(f"🔧 Initializing Firecrawl scraper with {timeout_seconds}s timeout and {cache_hours}h cache...")
        scraper = JobScraper(timeout_seconds=timeout_seconds, cache_hours=cache_hours)
        print("✅ Firecrawl scraper with intelligent caching initialized successfully!")
        return scraper
    except ValueError as e:
        print(f"❌ Failed to create scraper: {str(e)}")
        print("🔄 Will use mock scraping instead")
        logger.error(f"Failed to create scraper: {str(e)}")
        return None
